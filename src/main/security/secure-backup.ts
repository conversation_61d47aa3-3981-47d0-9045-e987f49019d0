import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { securityConfig } from './security-config';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';

export interface BackupMetadata {
  id: string;
  timestamp: Date;
  version: string;
  size: number;
  checksum: string;
  encrypted: boolean;
  compressionType: 'none' | 'gzip' | 'brotli';
}

export interface SecureBackupConfig {
  encryptionEnabled: boolean;
  compressionEnabled: boolean;
  maxBackups: number;
  backupInterval: number;
  backupPath: string;
  encryptionKey: string;
  encryptionAlgorithm: string;
}

export class SecureBackupManager {
  private static instance: SecureBackupManager;
  private config: SecureBackupConfig;
  private backupTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.config = this.loadBackupConfig();
    this.ensureBackupDirectory();
  }

  public static getInstance(): SecureBackupManager {
    if (!SecureBackupManager.instance) {
      SecureBackupManager.instance = new SecureBackupManager();
    }
    return SecureBackupManager.instance;
  }

  private loadBackupConfig(): SecureBackupConfig {
    const secConfig = securityConfig.getSecurityConfig();
    
    return {
      encryptionEnabled: secConfig.encryptionEnabled,
      compressionEnabled: true,
      maxBackups: parseInt(process.env.MAX_BACKUPS || '10'),
      backupInterval: parseInt(process.env.BACKUP_INTERVAL || '3600000'), // 1 hour
      backupPath: path.join(app.getPath('userData'), 'backups'),
      encryptionKey: process.env.BACKUP_ENCRYPTION_KEY || secConfig.encryptionKey,
      encryptionAlgorithm: secConfig.encryptionAlgorithm
    };
  }

  private ensureBackupDirectory(): void {
    if (!fs.existsSync(this.config.backupPath)) {
      fs.mkdirSync(this.config.backupPath, { recursive: true });
      this.log('Backup directory created', 'info');
    }
  }

  public startAutomaticBackups(): void {
    if (this.backupTimer) {
      this.stopAutomaticBackups();
    }

    this.backupTimer = setInterval(async () => {
      try {
        await this.createBackup();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.log(`Automatic backup failed: ${errorMessage}`, 'error');
      }
    }, this.config.backupInterval);

    this.log('Automatic backups started', 'info');
  }

  public stopAutomaticBackups(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
      this.backupTimer = null;
      this.log('Automatic backups stopped', 'info');
    }
  }

  public async createBackup(sourcePath?: string): Promise<string> {
    try {
      const backupId = this.generateBackupId();
      const timestamp = new Date();
      
      // Default to database path if not specified
      const sourceFile = sourcePath || process.env.DATABASE_PATH || './data/prod.db';
      
      if (!fs.existsSync(sourceFile)) {
        throw new Error(`Source file not found: ${sourceFile}`);
      }

      this.log(`Creating secure backup: ${backupId}`, 'info');

      // Read source data
      const sourceData = fs.readFileSync(sourceFile);
      
      // Compress data if enabled
      let processedData: Buffer = sourceData;
      let compressionType: 'none' | 'gzip' | 'brotli' = 'none';

      if (this.config.compressionEnabled) {
        processedData = await this.compressData(sourceData);
        compressionType = 'gzip';
      }

      // Encrypt data if enabled
      let finalData: Buffer = processedData;
      let encrypted = false;

      if (this.config.encryptionEnabled) {
        finalData = this.encryptData(processedData);
        encrypted = true;
      }

      // Generate checksum
      const checksum = crypto.createHash('sha256').update(finalData).digest('hex');

      // Create backup metadata
      const metadata: BackupMetadata = {
        id: backupId,
        timestamp,
        version: app.getVersion(),
        size: finalData.length,
        checksum,
        encrypted,
        compressionType
      };

      // Save backup file
      const backupFileName = `backup_${backupId}.bak`;
      const backupFilePath = path.join(this.config.backupPath, backupFileName);
      fs.writeFileSync(backupFilePath, finalData);

      // Save metadata
      const metadataFileName = `backup_${backupId}.meta`;
      const metadataFilePath = path.join(this.config.backupPath, metadataFileName);
      fs.writeFileSync(metadataFilePath, JSON.stringify(metadata, null, 2));

      // Log successful backup
      await auditLogger.logSecurityEvent(
        'BACKUP_CREATED' as AuditEventType,
        AuditSeverity.LOW,
        { metadata },
        undefined,
        `Secure backup created: ${backupId}`
      );

      this.log(`Backup created successfully: ${backupId}`, 'info');

      // Clean up old backups
      await this.cleanupOldBackups();

      return backupId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : 'No stack trace';
      this.log(`Backup creation failed: ${errorMessage}`, 'error');

      await auditLogger.logSecurityEvent(
        'BACKUP_FAILED' as AuditEventType,
        AuditSeverity.MEDIUM,
        { error: errorStack },
        undefined,
        `Backup creation failed: ${errorMessage}`
      );
      
      throw error;
    }
  }

  public async restoreBackup(backupId: string, targetPath?: string): Promise<void> {
    try {
      this.log(`Restoring backup: ${backupId}`, 'info');

      // Load backup metadata
      const metadata = this.loadBackupMetadata(backupId);
      
      // Load backup data
      const backupFileName = `backup_${backupId}.bak`;
      const backupFilePath = path.join(this.config.backupPath, backupFileName);
      
      if (!fs.existsSync(backupFilePath)) {
        throw new Error(`Backup file not found: ${backupFileName}`);
      }

      let backupData: Buffer = fs.readFileSync(backupFilePath);

      // Verify checksum
      const actualChecksum = crypto.createHash('sha256').update(backupData).digest('hex');
      if (actualChecksum !== metadata.checksum) {
        throw new Error('Backup integrity check failed: checksum mismatch');
      }

      // Decrypt data if encrypted
      if (metadata.encrypted) {
        backupData = this.decryptData(backupData);
      }

      // Decompress data if compressed
      if (metadata.compressionType !== 'none') {
        backupData = await this.decompressData(backupData, metadata.compressionType);
      }

      // Write restored data
      const restoreTarget = targetPath || process.env.DATABASE_PATH || './data/prod.db';
      
      // Create backup of current file before restore
      if (fs.existsSync(restoreTarget)) {
        const currentBackupPath = `${restoreTarget}.pre-restore.${Date.now()}`;
        fs.copyFileSync(restoreTarget, currentBackupPath);
        this.log(`Current file backed up to: ${currentBackupPath}`, 'info');
      }

      fs.writeFileSync(restoreTarget, backupData);

      // Log successful restore
      await auditLogger.logSecurityEvent(
        'BACKUP_RESTORED' as AuditEventType,
        AuditSeverity.MEDIUM,
        { backupId, targetPath: restoreTarget, metadata },
        undefined,
        `Backup restored successfully: ${backupId}`
      );

      this.log(`Backup restored successfully: ${backupId}`, 'info');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : 'No stack trace';
      this.log(`Backup restore failed: ${errorMessage}`, 'error');

      await auditLogger.logSecurityEvent(
        'BACKUP_RESTORE_FAILED' as AuditEventType,
        AuditSeverity.HIGH,
        { backupId, error: errorStack },
        undefined,
        `Backup restore failed: ${errorMessage}`
      );
      
      throw error;
    }
  }

  private encryptData(data: Buffer): Buffer {
    try {
      const algorithm = this.config.encryptionAlgorithm;
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      const iv = crypto.randomBytes(16);

      const cipher = crypto.createCipheriv(algorithm, key, iv);
      cipher.setAutoPadding(true);

      const encrypted = Buffer.concat([
        iv,
        cipher.update(data),
        cipher.final()
      ]);

      return encrypted;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Encryption failed: ${errorMessage}`);
    }
  }

  private decryptData(encryptedData: Buffer): Buffer {
    try {
      const algorithm = this.config.encryptionAlgorithm;
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      const iv = encryptedData.slice(0, 16);
      const encrypted = encryptedData.slice(16);

      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      decipher.setAutoPadding(true);

      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);

      return decrypted;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Decryption failed: ${errorMessage}`);
    }
  }

  private async compressData(data: Buffer): Promise<Buffer> {
    const zlib = require('zlib');
    return new Promise((resolve, reject) => {
      zlib.gzip(data, (error: Error | null, compressed: Buffer) => {
        if (error) {
          reject(new Error(`Compression failed: ${error.message}`));
        } else {
          resolve(compressed);
        }
      });
    });
  }

  private async decompressData(data: Buffer, type: 'gzip' | 'brotli'): Promise<Buffer> {
    const zlib = require('zlib');
    return new Promise((resolve, reject) => {
      if (type === 'gzip') {
        zlib.gunzip(data, (error: Error | null, decompressed: Buffer) => {
          if (error) {
            reject(new Error(`Decompression failed: ${error.message}`));
          } else {
            resolve(decompressed);
          }
        });
      } else {
        reject(new Error(`Unsupported compression type: ${type}`));
      }
    });
  }

  private loadBackupMetadata(backupId: string): BackupMetadata {
    const metadataFileName = `backup_${backupId}.meta`;
    const metadataFilePath = path.join(this.config.backupPath, metadataFileName);
    
    if (!fs.existsSync(metadataFilePath)) {
      throw new Error(`Backup metadata not found: ${metadataFileName}`);
    }

    const metadataContent = fs.readFileSync(metadataFilePath, 'utf8');
    return JSON.parse(metadataContent);
  }

  public listBackups(): BackupMetadata[] {
    try {
      const backups: BackupMetadata[] = [];
      const files = fs.readdirSync(this.config.backupPath);
      
      for (const file of files) {
        if (file.endsWith('.meta')) {
          const metadataPath = path.join(this.config.backupPath, file);
          const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
          backups.push(metadata);
        }
      }

      // Sort by timestamp (newest first)
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`Failed to list backups: ${errorMessage}`, 'error');
      return [];
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = this.listBackups();
      
      if (backups.length > this.config.maxBackups) {
        const backupsToDelete = backups.slice(this.config.maxBackups);
        
        for (const backup of backupsToDelete) {
          await this.deleteBackup(backup.id);
        }
        
        this.log(`Cleaned up ${backupsToDelete.length} old backups`, 'info');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`Backup cleanup failed: ${errorMessage}`, 'error');
    }
  }

  public async deleteBackup(backupId: string): Promise<void> {
    try {
      const backupFileName = `backup_${backupId}.bak`;
      const metadataFileName = `backup_${backupId}.meta`;
      
      const backupFilePath = path.join(this.config.backupPath, backupFileName);
      const metadataFilePath = path.join(this.config.backupPath, metadataFileName);
      
      if (fs.existsSync(backupFilePath)) {
        fs.unlinkSync(backupFilePath);
      }
      
      if (fs.existsSync(metadataFilePath)) {
        fs.unlinkSync(metadataFilePath);
      }
      
      this.log(`Backup deleted: ${backupId}`, 'info');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`Failed to delete backup ${backupId}: ${errorMessage}`, 'error');
      throw error;
    }
  }

  private generateBackupId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${timestamp}_${random}`;
  }

  private log(message: string, level: 'info' | 'warning' | 'error'): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [SECURE-BACKUP] ${message}`);
  }
}

export const secureBackupManager = SecureBackupManager.getInstance();
